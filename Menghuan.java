package com.xiaofeng.menghuan;

import com.github.unidbg.AndroidEmulator;
import com.github.unidbg.Module;
import com.github.unidbg.linux.android.AndroidEmulatorBuilder;
import com.github.unidbg.linux.android.AndroidResolver;
import com.github.unidbg.linux.android.dvm.*;
import com.github.unidbg.linux.android.dvm.api.ApplicationInfo;
import com.github.unidbg.linux.android.dvm.array.ArrayObject;
import com.github.unidbg.linux.android.dvm.jni.ProxyDvmObject;
import com.github.unidbg.memory.Memory;
import com.github.unidbg.virtualmodule.android.AndroidModule;

import java.io.File;
import java.util.*;

public class Menghuan extends AbstractJni {
    public static AndroidEmulator emulator;
    public static Memory memory;
    public static VM vm;
    public static Module module;

    public Menghuan() {
        emulator = AndroidEmulatorBuilder.for64Bit().setProcessName("com.netease.xyqcbg").build();
        memory = emulator.getMemory();
        memory.setLibraryResolver(new AndroidResolver(23));
        vm = emulator.createDalvikVM(new File("apks/menghuan/v5.81.0.apk"));
        vm.setJni(this);
        new AndroidModule(emulator, vm).register(memory);
        vm.setVerbose(true);
        DalvikModule dm = vm.loadLibrary("netseckit-4.2.3", true);
        module = dm.getModule();
        System.out.println("[*] loading!! module's baseAddr is: 0x" + Long.toHexString(module.base));
//         emulator.attach().addBreakPoint(module.base + 0x162f4);
        dm.callJNI_OnLoad(emulator);
    }


    public String sig() {
        DvmClass class_JNIFactory = vm.resolveClass("com/netease/NetSecKit/factory/JNIFactory");
        String method_w3270a03dafee4a0a = "w3270a03dafee4a0a(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;";

        DvmObject<?> obj = class_JNIFactory.callStaticJniMethodObject(
                emulator,
                method_w3270a03dafee4a0a,
                vm.resolveClass("android/content/Context").newObject(null),
                "48de41784dad6ff20e42640f2d5b0151"
        );
        String result = obj.getValue().toString();
        System.out.println("res: " + result);
        return result;
    }


    public static void main(String[] args) {
        Menghuan menghuan = new Menghuan();
        menghuan.sig();
    }

    @Override
    public int getStaticIntField(BaseVM vm, DvmClass dvmClass, String signature) {
        switch (signature) {
            case "com/netease/NetSecKit/poly/a->o:I": {
                // 1. java.lang.UnsupportedOperationException: com/netease/NetSecKit/poly/a->o:I
                //	at com.github.unidbg.linux.android.dvm.AbstractJni.getStaticIntField(AbstractJni.java:136)
                // jadx中硬编码了
                return 2;
            }
            case "com/netease/NetSecKit/poly/a->p:I": {
                // 2. java.lang.UnsupportedOperationException: com/netease/NetSecKit/poly/a->p:I
                //	at com.github.unidbg.linux.android.dvm.AbstractJni.getStaticIntField(AbstractJni.java:136)
                // 没找到赋值位置，默认是0
                return 12;
            }
        }
        return super.getStaticIntField(vm, dvmClass, signature);
    }

    @Override
    public DvmObject<?> callObjectMethodV(BaseVM vm, DvmObject<?> dvmObject, String signature, VaList vaList) {
        switch (signature) {
            case "android/content/Context->getApplicationInfo()Landroid/content/pm/ApplicationInfo;":{
                // 3. java.lang.UnsupportedOperationException: android/content/Context->getApplicationInfo()Landroid/content/pm/ApplicationInfo;
                //	at com.github.unidbg.linux.android.dvm.AbstractJni.callObjectMethodV(AbstractJni.java:417)
                return vm.resolveClass("android/content/pm/ApplicationInfo").newObject(null);
            }
        }
        return super.callObjectMethodV(vm, dvmObject, signature, vaList);
    }

    @Override
    public DvmObject<?> getObjectField(BaseVM vm, DvmObject<?> dvmObject, String signature) {
        switch (signature) {
            case "android/content/pm/ApplicationInfo->publicSourceDir:Ljava/lang/String;": {
                // 4. java.lang.UnsupportedOperationException: android/content/pm/ApplicationInfo->publicSourceDir:Ljava/lang/String;
                //	at com.github.unidbg.linux.android.dvm.AbstractJni.getObjectField(AbstractJni.java:171)
                return new StringObject(vm, "/data/app/com.netease.xyqcbg/base.apk");
            }
        }
        return super.getObjectField(vm, dvmObject, signature);
    }
}
