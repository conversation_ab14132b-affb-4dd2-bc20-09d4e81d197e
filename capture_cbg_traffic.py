#!/usr/bin/env python3
# 梦幻藏宝阁网络流量抓包分析脚本
# 使用方法: mitmproxy -s capture_cbg_traffic.py

from mitmproxy import http
import json
import re
import base64
import hashlib
import time
from urllib.parse import parse_qs, urlparse

class CBGTrafficAnalyzer:
    def __init__(self):
        self.cbg_requests = []
        self.sign_patterns = []
        
    def request(self, flow: http.HTTPFlow) -> None:
        """分析HTTP请求"""
        
        # 检查是否是梦幻藏宝阁相关的请求
        if self.is_cbg_request(flow):
            print(f"\n[发现] CBG请求: {flow.request.pretty_url}")
            
            # 分析请求头
            self.analyze_headers(flow)
            
            # 分析请求体
            self.analyze_body(flow)
            
            # 分析URL参数
            self.analyze_url_params(flow)
            
            # 保存完整请求
            self.save_request(flow)
    
    def response(self, flow: http.HTTPFlow) -> None:
        """分析HTTP响应"""
        if self.is_cbg_request(flow):
            print(f"[响应] 状态码: {flow.response.status_code}")
            
            # 分析响应头
            if flow.response.headers:
                for name, value in flow.response.headers.items():
                    if any(keyword in name.lower() for keyword in ['sign', 'auth', 'token']):
                        print(f"[响应头] {name}: {value}")
    
    def is_cbg_request(self, flow: http.HTTPFlow) -> bool:
        """判断是否是CBG相关请求"""
        url = flow.request.pretty_url.lower()
        host = flow.request.pretty_host.lower()
        
        cbg_keywords = [
            'cbg', 'xyq', 'netease', 'menghuan', 
            '藏宝阁', 'treasure', 'fantasy'
        ]
        
        return any(keyword in url or keyword in host for keyword in cbg_keywords)
    
    def analyze_headers(self, flow: http.HTTPFlow) -> None:
        """分析请求头中的签名信息"""
        print("[分析] 请求头:")
        
        for name, value in flow.request.headers.items():
            # 查找签名相关的头部
            if any(keyword in name.lower() for keyword in ['sign', 'auth', 'token', 'cbg']):
                print(f"  [KEY] {name}: {value}")
                
                # 特别关注cbg-auth-sign
                if 'cbg-auth-sign' in name.lower():
                    self.analyze_signature(value, "Header: " + name)
            
            # 查找其他重要头部
            elif name.lower() in ['user-agent', 'x-requested-with', 'content-type']:
                print(f"  [MOBILE] {name}: {value}")
    
    def analyze_body(self, flow: http.HTTPFlow) -> None:
        """分析请求体中的签名信息"""
        if not flow.request.content:
            return
            
        try:
            body_text = flow.request.text
            print(f"[分析] 请求体长度: {len(body_text)}")
            
            # 尝试解析JSON
            try:
                body_json = json.loads(body_text)
                self.analyze_json_body(body_json)
            except:
                # 尝试解析表单数据
                if 'application/x-www-form-urlencoded' in flow.request.headers.get('content-type', ''):
                    self.analyze_form_body(body_text)
                else:
                    # 直接搜索签名模式
                    self.search_signature_patterns(body_text, "请求体")
                    
        except Exception as e:
            print(f"[错误] 请求体分析失败: {e}")
    
    def analyze_json_body(self, json_data: dict) -> None:
        """分析JSON格式的请求体"""
        print("[分析] JSON请求体:")
        
        def search_json_recursive(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # 查找签名相关字段
                    if any(keyword in key.lower() for keyword in ['sign', 'auth', 'token', 'cbg']):
                        print(f"  [KEY] {current_path}: {value}")
                        if isinstance(value, str):
                            self.analyze_signature(value, f"JSON: {current_path}")
                    
                    search_json_recursive(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    search_json_recursive(item, f"{path}[{i}]")
        
        search_json_recursive(json_data)
    
    def analyze_form_body(self, body_text: str) -> None:
        """分析表单格式的请求体"""
        print("[分析] 表单请求体:")
        
        try:
            form_data = parse_qs(body_text)
            for key, values in form_data.items():
                value = values[0] if values else ""
                
                if any(keyword in key.lower() for keyword in ['sign', 'auth', 'token', 'cbg']):
                    print(f"  [KEY] {key}: {value}")
                    self.analyze_signature(value, f"表单: {key}")
                else:
                    print(f"  [NOTE] {key}: {value[:100]}{'...' if len(value) > 100 else ''}")
        except Exception as e:
            print(f"[错误] 表单解析失败: {e}")
    
    def analyze_url_params(self, flow: http.HTTPFlow) -> None:
        """分析URL参数中的签名信息"""
        parsed_url = urlparse(flow.request.pretty_url)
        if parsed_url.query:
            print("[分析] URL参数:")
            params = parse_qs(parsed_url.query)
            
            for key, values in params.items():
                value = values[0] if values else ""
                
                if any(keyword in key.lower() for keyword in ['sign', 'auth', 'token', 'cbg']):
                    print(f"  [KEY] {key}: {value}")
                    self.analyze_signature(value, f"URL参数: {key}")
                else:
                    print(f"  [NOTE] {key}: {value[:50]}{'...' if len(value) > 50 else ''}")
    
    def analyze_signature(self, signature: str, source: str) -> None:
        """深度分析签名值"""
        if not signature or len(signature) == 0:
            print(f"  [ERROR] {source} - 空签名")
            return
            
        print(f"\n[签名分析] 来源: {source}")
        print(f"[签名分析] 值: {signature}")
        print(f"[签名分析] 长度: {len(signature)}")
        
        # 分析签名格式
        self.analyze_signature_format(signature)
        
        # 保存签名模式
        self.sign_patterns.append({
            'source': source,
            'value': signature,
            'length': len(signature),
            'timestamp': time.time()
        })
    
    def analyze_signature_format(self, signature: str) -> None:
        """分析签名的格式特征"""
        
        # 检查是否是Base64
        try:
            decoded = base64.b64decode(signature)
            if base64.b64encode(decoded).decode() == signature:
                print(f"  [OK] Base64格式 (解码长度: {len(decoded)})")
                print(f"  [NOTE] 解码内容: {decoded.hex()}")
        except:
            print(f"  [ERROR] 非Base64格式")
        
        # 检查是否是十六进制
        try:
            bytes.fromhex(signature)
            print(f"  [OK] 十六进制格式 (字节长度: {len(signature)//2})")
        except:
            print(f"  [ERROR] 非十六进制格式")
        
        # 检查常见哈希长度
        hash_lengths = {
            32: "MD5",
            40: "SHA1", 
            64: "SHA256",
            128: "SHA512"
        }
        
        if len(signature) in hash_lengths:
            print(f"  [SEARCH] 可能是{hash_lengths[len(signature)]}哈希")
        
        # 分析字符组成
        char_types = {
            'digits': sum(c.isdigit() for c in signature),
            'lowercase': sum(c.islower() for c in signature),
            'uppercase': sum(c.isupper() for c in signature),
            'special': sum(not c.isalnum() for c in signature)
        }
        
        print(f"  [STATS] 字符组成: {char_types}")
    
    def search_signature_patterns(self, text: str, source: str) -> None:
        """在文本中搜索可能的签名模式"""
        
        # 常见签名模式
        patterns = [
            (r'[a-f0-9]{32}', 'MD5哈希'),
            (r'[a-f0-9]{40}', 'SHA1哈希'),
            (r'[a-f0-9]{64}', 'SHA256哈希'),
            (r'[A-Za-z0-9+/]{20,}={0,2}', 'Base64'),
            (r'cbg[_-]?auth[_-]?sign["\']?\s*[:=]\s*["\']?([^"\'&\s]+)', 'CBG签名'),
        ]
        
        for pattern, desc in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                print(f"  [FOUND] 发现{desc}: {match}")
                self.analyze_signature(match, f"{source} - {desc}")
    
    def save_request(self, flow: http.HTTPFlow) -> None:
        """保存完整的请求信息"""
        request_data = {
            'timestamp': time.time(),
            'url': flow.request.pretty_url,
            'method': flow.request.method,
            'headers': dict(flow.request.headers),
            'body': flow.request.text if flow.request.content else None,
            'response_status': flow.response.status_code if flow.response else None
        }
        
        self.cbg_requests.append(request_data)
        
        # 保存到文件
        try:
            with open('cbg_requests.json', 'w', encoding='utf-8') as f:
                json.dump(self.cbg_requests, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"[错误] 保存请求失败: {e}")

# 创建分析器实例
analyzer = CBGTrafficAnalyzer()

def request(flow: http.HTTPFlow) -> None:
    analyzer.request(flow)

def response(flow: http.HTTPFlow) -> None:
    analyzer.response(flow)

print("[*] CBG流量分析脚本已加载")
print("[*] 使用方法: mitmproxy -s capture_cbg_traffic.py")
print("[*] 配置手机代理后，打开梦幻藏宝阁应用进行操作")
