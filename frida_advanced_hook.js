// 梦幻藏宝阁 cbg-auth-sign 高级Frida Hook脚本
// 使用方法: frida -U -f com.netease.xyqcbg -l frida_advanced_hook.js --no-pause

console.log("[*] ========================================");
console.log("[*]     梦幻藏宝阁 cbg-auth-sign Hook");
console.log("[*] ========================================");

// 全局变量
var successfulSigns = [];
var hookResults = {};

// 工具函数
function bytesToHex(bytes) {
    var hex = "";
    for (var i = 0; i < bytes.length; i++) {
        hex += ("0" + (bytes[i] & 0xFF).toString(16)).slice(-2);
    }
    return hex;
}

function saveToFile(filename, content) {
    try {
        var file = new File("/sdcard/" + filename, "w");
        file.write(content);
        file.close();
        console.log("[保存] 数据已保存到: /sdcard/" + filename);
    } catch (e) {
        console.log("[错误] 保存文件失败: " + e);
    }
}

// Hook JNI方法注册
function hookJNIRegistration() {
    console.log("[*] Hook JNI方法注册...");
    
    var RegisterNatives = Module.findExportByName("libart.so", "_ZN3art3JNI15RegisterNativesEP7_JNIEnvP7_jclassPK15JNINativeMethodi");
    if (RegisterNatives) {
        Interceptor.attach(RegisterNatives, {
            onEnter: function(args) {
                var env = args[0];
                var clazz = args[1];
                var methods = args[2];
                var nMethods = args[3].toInt32();
                
                // 获取类名
                var className = Java.vm.tryGetEnv().getClassName(clazz);
                
                if (className && className.includes("JNIFactory")) {
                    console.log("[JNI注册] 类: " + className + ", 方法数: " + nMethods);
                    
                    // 解析注册的方法
                    for (var i = 0; i < nMethods; i++) {
                        var methodInfo = methods.add(i * Process.pointerSize * 3);
                        var methodName = methodInfo.readPointer().readCString();
                        var signature = methodInfo.add(Process.pointerSize).readPointer().readCString();
                        var fnPtr = methodInfo.add(Process.pointerSize * 2).readPointer();
                        
                        console.log("[JNI注册] 方法: " + methodName + signature + " -> " + fnPtr);
                        
                        // Hook签名相关方法
                        if (methodName.includes("w3270a03dafee4a0a") || 
                            methodName.includes("sign") || 
                            methodName.includes("auth")) {
                            hookNativeMethod(fnPtr, methodName);
                        }
                    }
                }
            }
        });
    }
}

// Hook原生方法
function hookNativeMethod(address, methodName) {
    console.log("[*] Hook原生方法: " + methodName + " @ " + address);
    
    try {
        Interceptor.attach(address, {
            onEnter: function(args) {
                console.log("[Hook进入] " + methodName);
                console.log("  参数0 (JNIEnv): " + args[0]);
                console.log("  参数1 (jobject): " + args[1]);
                console.log("  参数2 (jstring): " + args[2]);
                
                // 尝试读取字符串参数
                try {
                    var env = Java.vm.getEnv();
                    var jstr = args[2];
                    if (jstr && !jstr.isNull()) {
                        var strPtr = env.getStringUtfChars(jstr, null);
                        if (strPtr && !strPtr.isNull()) {
                            var paramStr = strPtr.readCString();
                            console.log("  字符串参数: " + paramStr);
                            this.inputParam = paramStr;
                        }
                    }
                } catch (e) {
                    console.log("  [错误] 读取参数失败: " + e);
                }
            },
            onLeave: function(retval) {
                console.log("[Hook退出] " + methodName);
                console.log("  返回值: " + retval);
                
                // 尝试读取返回的字符串
                try {
                    if (retval && !retval.isNull()) {
                        var env = Java.vm.getEnv();
                        var strPtr = env.getStringUtfChars(retval, null);
                        if (strPtr && !strPtr.isNull()) {
                            var resultStr = strPtr.readCString();
                            console.log("  返回字符串: " + resultStr);
                            
                            if (resultStr && resultStr.length > 0) {
                                console.log("[SUCCESS] 成功获取签名: " + resultStr);
                                
                                var result = {
                                    method: methodName,
                                    input: this.inputParam || "unknown",
                                    output: resultStr,
                                    timestamp: new Date().toISOString()
                                };
                                
                                successfulSigns.push(result);
                                
                                // 保存成功的签名
                                var logContent = JSON.stringify(result, null, 2) + "\n";
                                saveToFile("cbg_sign_success.log", logContent);
                            }
                        }
                    }
                } catch (e) {
                    console.log("  [错误] 读取返回值失败: " + e);
                }
            }
        });
    } catch (e) {
        console.log("[错误] Hook方法失败: " + e);
    }
}

// Hook Java层JNIFactory
function hookJavaJNIFactory() {
    console.log("[*] Hook Java层JNIFactory...");
    
    Java.perform(function() {
        try {
            var JNIFactory = Java.use("com.netease.NetSecKit.factory.JNIFactory");
            
            // 获取所有方法
            var methods = JNIFactory.class.getDeclaredMethods();
            console.log("[*] JNIFactory方法数量: " + methods.length);
            
            for (var i = 0; i < methods.length; i++) {
                var method = methods[i];
                var methodName = method.getName();
                console.log("[*] 发现方法: " + methodName);
                
                // Hook所有w开头的方法（混淆后的方法）
                if (methodName.startsWith("w") && methodName.length > 10) {
                    try {
                        console.log("[*] Hook方法: " + methodName);
                        
                        JNIFactory[methodName].implementation = function() {
                            console.log("[Java Hook] 调用方法: " + methodName);
                            console.log("[Java Hook] 参数数量: " + arguments.length);
                            
                            for (var j = 0; j < arguments.length; j++) {
                                console.log("[Java Hook] 参数" + j + ": " + arguments[j]);
                            }
                            
                            var result = this[methodName].apply(this, arguments);
                            console.log("[Java Hook] 返回值: " + result);
                            
                            if (result && result.length > 0) {
                                console.log("[SUCCESS] Java层获取签名: " + result);
                                
                                var logData = {
                                    method: methodName,
                                    args: Array.prototype.slice.call(arguments),
                                    result: result,
                                    timestamp: new Date().toISOString()
                                };
                                
                                successfulSigns.push(logData);
                                saveToFile("cbg_java_sign_success.log", JSON.stringify(logData, null, 2));
                            }
                            
                            return result;
                        };
                    } catch (e) {
                        console.log("[错误] Hook Java方法失败: " + methodName + " - " + e);
                    }
                }
            }
        } catch (e) {
            console.log("[错误] 找不到JNIFactory类: " + e);
        }
    });
}

// Hook HTTP请求
function hookHTTPRequests() {
    console.log("[*] Hook HTTP请求...");
    
    Java.perform(function() {
        // Hook OkHttp
        try {
            var OkHttpClient = Java.use("okhttp3.OkHttpClient");
            var Request = Java.use("okhttp3.Request");
            
            OkHttpClient.newCall.implementation = function(request) {
                console.log("[HTTP] OkHttp请求: " + request.url());
                
                // 检查请求头
                var headers = request.headers();
                var headerNames = headers.names();
                var headerArray = headerNames.toArray();
                
                for (var i = 0; i < headerArray.length; i++) {
                    var headerName = headerArray[i];
                    var headerValue = headers.get(headerName);
                    
                    if (headerName.toLowerCase().includes("sign") || 
                        headerName.toLowerCase().includes("auth") ||
                        headerName.toLowerCase().includes("cbg")) {
                        console.log("[HTTP Header] " + headerName + ": " + headerValue);
                        
                        if (headerName.toLowerCase().includes("cbg-auth-sign")) {
                            console.log("[SUCCESS] 发现cbg-auth-sign: " + headerValue);
                            
                            var httpResult = {
                                url: request.url().toString(),
                                header: headerName,
                                value: headerValue,
                                timestamp: new Date().toISOString()
                            };
                            
                            successfulSigns.push(httpResult);
                            saveToFile("cbg_http_sign.log", JSON.stringify(httpResult, null, 2));
                        }
                    }
                }
                
                return this.newCall(request);
            };
        } catch (e) {
            console.log("[错误] Hook OkHttp失败: " + e);
        }
        
        // Hook HttpURLConnection
        try {
            var HttpURLConnection = Java.use("java.net.HttpURLConnection");
            
            HttpURLConnection.setRequestProperty.implementation = function(key, value) {
                if (key && (key.toLowerCase().includes("sign") || 
                           key.toLowerCase().includes("auth") ||
                           key.toLowerCase().includes("cbg"))) {
                    console.log("[HTTP Property] " + key + ": " + value);
                    
                    if (key.toLowerCase().includes("cbg-auth-sign")) {
                        console.log("[SUCCESS] HttpURLConnection发现cbg-auth-sign: " + value);
                        
                        var httpResult = {
                            method: "HttpURLConnection",
                            header: key,
                            value: value,
                            timestamp: new Date().toISOString()
                        };
                        
                        successfulSigns.push(httpResult);
                        saveToFile("cbg_http_connection_sign.log", JSON.stringify(httpResult, null, 2));
                    }
                }
                
                return this.setRequestProperty(key, value);
            };
        } catch (e) {
            console.log("[错误] Hook HttpURLConnection失败: " + e);
        }
    });
}

// Hook文件操作
function hookFileOperations() {
    console.log("[*] Hook文件操作...");
    
    // Hook native文件操作
    var openPtr = Module.findExportByName("libc.so", "open");
    if (openPtr) {
        Interceptor.attach(openPtr, {
            onEnter: function(args) {
                var path = args[0].readCString();
                if (path && (path.includes(".apk") || path.includes("cert") || path.includes("sign"))) {
                    console.log("[文件] 打开文件: " + path);
                    this.path = path;
                }
            },
            onLeave: function(retval) {
                if (this.path) {
                    console.log("[文件] 文件句柄: " + retval + " (路径: " + this.path + ")");
                }
            }
        });
    }
}

// 主Hook函数
function main() {
    console.log("[*] 开始Hook梦幻藏宝阁...");
    
    // 等待应用启动
    setTimeout(function() {
        hookJNIRegistration();
        hookJavaJNIFactory();
        hookHTTPRequests();
        hookFileOperations();
        
        console.log("[*] 所有Hook已设置完成");
        console.log("[*] 请在应用中进行登录或其他操作以触发签名生成");
        
        // 定期报告结果
        setInterval(function() {
            if (successfulSigns.length > 0) {
                console.log("[报告] 已捕获 " + successfulSigns.length + " 个成功的签名");
                console.log("[报告] 最新签名: " + JSON.stringify(successfulSigns[successfulSigns.length - 1], null, 2));
            }
        }, 10000); // 每10秒报告一次
        
    }, 2000);
}

// 启动Hook
main();
