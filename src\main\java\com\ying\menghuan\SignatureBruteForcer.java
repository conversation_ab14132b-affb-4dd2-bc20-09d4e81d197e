package com.ying.menghuan;

import com.github.unidbg.AndroidEmulator;
import com.github.unidbg.linux.android.AndroidEmulatorBuilder;
import com.github.unidbg.linux.android.AndroidResolver;
import com.github.unidbg.linux.android.dvm.*;
import com.github.unidbg.memory.Memory;
import com.github.unidbg.Module;

import java.io.File;
import java.util.*;
import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;

/**
 * 梦幻藏宝阁 cbg-auth-sign 签名爆破器
 * 专门用于暴力破解和智能测试签名参数
 */
public class SignatureBruteForcer extends AbstractJni {
    private final AndroidEmulator emulator;
    private final VM vm;
    private final Module module;
    
    public SignatureBruteForcer() {
        // 创建模拟器实例
        emulator = AndroidEmulatorBuilder.for64Bit().setProcessName("com.netease.xyqcbg").build();
        
        final Memory memory = emulator.getMemory();
        memory.setLibraryResolver(new AndroidResolver(23));
        
        vm = emulator.createDalvikVM(new File("apks/menghuan/v5.81.0.apk"));
        
        System.out.println("[爆破器] 初始化完成");
        
        // 加载目标库
        module = emulator.loadLibrary(new File("apks/menghuan/lib/arm64-v8a/libnetsecsdk.so"));
        
        // 调用JNI_OnLoad
        module.callFunction(emulator, "JNI_OnLoad", vm.getJNIEnv(), null);
        
        System.out.println("[爆破器] 库加载完成，开始签名爆破");
    }
    
    public static void main(String[] args) {
        SignatureBruteForcer bruteForcer = new SignatureBruteForcer();
        bruteForcer.startBruteForce();
    }
    
    // 开始暴力破解
    public void startBruteForce() {
        System.out.println("\n[*] ========== 开始cbg-auth-sign签名爆破 ==========");
        
        // 1. 智能参数生成
        List<String> intelligentParams = generateIntelligentParams();
        
        // 2. 暴力参数生成
        List<String> bruteForceParams = generateBruteForceParams();
        
        // 3. 组合所有参数
        List<String> allParams = new ArrayList<>();
        allParams.addAll(intelligentParams);
        allParams.addAll(bruteForceParams);
        
        System.out.println("[爆破器] 总共生成 " + allParams.size() + " 个测试参数");
        
        // 4. 开始测试
        testAllParameters(allParams);
    }
    
    // 生成智能参数
    private List<String> generateIntelligentParams() {
        List<String> params = new ArrayList<>();
        
        System.out.println("[智能生成] 基于逆向分析生成参数...");
        
        // 基于APK信息
        params.add("0dbfed16914b56294b8e162cafcc1ff1"); // APK MD5
        params.add("9c55391bfac316e6c5d7f758d8de4500746da5bc"); // APK SHA1
        
        // 基于证书信息
        params.add("b31e6c5c7e76d54a2ac266b755cec0a2"); // 证书MD5
        params.add("a9aa23569b2df03f79ae0e1a0d1cd5aaebf07f0d"); // 证书SHA1
        
        // 基于应用信息
        params.add("com.netease.xyqcbg");
        params.add("netease");
        params.add("xyqcbg");
        params.add("cbg");
        params.add("menghuan");
        params.add("581");
        params.add("5.81.0");
        
        // 基于时间戳
        long currentTime = System.currentTimeMillis();
        params.add(String.valueOf(currentTime));
        params.add(String.valueOf(currentTime / 1000));
        params.add(String.valueOf(currentTime / 1000 / 60));
        
        // 基于常见密钥
        params.add("key");
        params.add("secret");
        params.add("token");
        params.add("sign");
        params.add("auth");
        params.add("password");
        params.add("123456");
        params.add("netease123");
        params.add("cbg2024");
        
        // 基于MD5哈希
        String[] hashSources = {"netease", "cbg", "xyq", "menghuan", "netseckit", "sign"};
        for (String source : hashSources) {
            params.add(md5(source));
            params.add(md5(source + "2024"));
            params.add(md5(source + "cbg"));
        }
        
        System.out.println("[智能生成] 生成了 " + params.size() + " 个智能参数");
        return params;
    }
    
    // 生成暴力破解参数
    private List<String> generateBruteForceParams() {
        List<String> params = new ArrayList<>();
        
        System.out.println("[暴力生成] 生成暴力破解参数...");
        
        // 1. 数字序列
        for (int i = 0; i < 1000; i++) {
            params.add(String.valueOf(i));
            params.add(String.format("%04d", i));
            params.add(String.format("%08d", i));
        }
        
        // 2. 十六进制序列
        for (int i = 0; i < 256; i++) {
            params.add(String.format("%02x", i));
            params.add(String.format("%04x", i));
            params.add(String.format("%08x", i));
        }
        
        // 3. 固定长度十六进制
        Random random = new Random();
        for (int len : new int[]{8, 16, 24, 32, 40, 48, 56, 64}) {
            for (int i = 0; i < 50; i++) {
                StringBuilder hex = new StringBuilder();
                for (int j = 0; j < len; j++) {
                    hex.append(String.format("%x", random.nextInt(16)));
                }
                params.add(hex.toString());
            }
        }
        
        // 4. 常见字符串组合
        String[] prefixes = {"", "0x", "netease_", "cbg_", "sign_", "auth_"};
        String[] suffixes = {"", "_key", "_secret", "_token", "_2024", "_v581"};
        String[] bases = {"test", "demo", "key", "secret", "sign", "auth", "token"};
        
        for (String prefix : prefixes) {
            for (String base : bases) {
                for (String suffix : suffixes) {
                    String param = prefix + base + suffix;
                    params.add(param);
                    params.add(param.toUpperCase());
                    params.add(md5(param));
                }
            }
        }
        
        System.out.println("[暴力生成] 生成了 " + params.size() + " 个暴力参数");
        return params;
    }
    
    // 测试所有参数
    private void testAllParameters(List<String> params) {
        System.out.println("\n[测试开始] 开始测试 " + params.size() + " 个参数...");
        
        int successCount = 0;
        int testCount = 0;
        
        try {
            DvmClass class_JNIFactory = vm.resolveClass("com/netease/NetSecKit/factory/JNIFactory");
            String method = "w3270a03dafee4a0a(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;";
            
            for (String param : params) {
                testCount++;
                
                if (testCount % 100 == 0) {
                    System.out.println("[进度] 已测试 " + testCount + "/" + params.size() + " 个参数");
                }
                
                try {
                    DvmObject<?> obj = class_JNIFactory.callStaticJniMethodObject(
                            emulator,
                            method,
                            vm.resolveClass("android/content/Context").newObject(null),
                            param
                    );
                    
                    String result = obj.getValue().toString();
                    
                    if (!result.isEmpty()) {
                        successCount++;
                        System.out.println("\n[SUCCESS] [突破成功] 参数: " + param);
                        System.out.println("[SUCCESS] [突破成功] 签名: " + result);
                        System.out.println("[SUCCESS] [突破成功] 长度: " + result.length());
                        
                        // 保存成功结果
                        saveSuccessResult(param, result);
                        
                        // 如果找到成功的，继续测试相似参数
                        testSimilarParams(param, result);
                    }
                    
                } catch (Exception e) {
                    // 忽略异常，继续测试下一个参数
                }
            }
            
        } catch (Exception e) {
            System.out.println("[错误] 测试过程异常: " + e.getMessage());
        }
        
        System.out.println("\n[测试完成] 总测试: " + testCount + ", 成功: " + successCount);
        
        if (successCount == 0) {
            System.out.println("[ERROR] 未找到有效签名，尝试其他方法...");
            tryAlternativeMethods();
        }
    }
    
    // 测试相似参数
    private void testSimilarParams(String successParam, String successResult) {
        System.out.println("\n[相似测试] 基于成功参数生成相似参数...");
        
        List<String> similarParams = new ArrayList<>();
        
        // 生成相似参数
        if (successParam.length() >= 8) {
            // 修改几个字符
            for (int i = 0; i < Math.min(10, successParam.length()); i++) {
                for (char c : "0123456789abcdefABCDEF".toCharArray()) {
                    StringBuilder similar = new StringBuilder(successParam);
                    similar.setCharAt(i, c);
                    similarParams.add(similar.toString());
                }
            }
        }
        
        // 添加前缀后缀
        String[] fixes = {"0", "1", "a", "f", "00", "ff", "key", "sign"};
        for (String fix : fixes) {
            similarParams.add(fix + successParam);
            similarParams.add(successParam + fix);
        }
        
        System.out.println("[相似测试] 生成了 " + similarParams.size() + " 个相似参数");
        testAllParameters(similarParams);
    }
    
    // 尝试其他方法
    private void tryAlternativeMethods() {
        System.out.println("\n[替代方法] 尝试其他签名方法...");
        
        try {
            DvmClass class_JNIFactory = vm.resolveClass("com/netease/NetSecKit/factory/JNIFactory");
            
            // 尝试其他方法
            String[] methods = {
                "w1228bcedf6204eeb(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;",
                "w183a9e5310205b79(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;",
                "w92bbe0960670a111(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;",
                "w534b93edf14c9e3b(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;",
                "we3f5598dde26215d(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;"
            };
            
            String testParam = "48de41784dad6ff20e42640f2d5b0151";
            
            for (String method : methods) {
                try {
                    System.out.println("[替代] 测试方法: " + method.split("\\(")[0]);
                    
                    DvmObject<?> obj = class_JNIFactory.callStaticJniMethodObject(
                            emulator,
                            method,
                            vm.resolveClass("android/content/Context").newObject(null),
                            testParam
                    );
                    
                    String result = obj.getValue().toString();
                    System.out.println("[替代] 结果: " + (result.isEmpty() ? "空字符串" : result));
                    
                    if (!result.isEmpty()) {
                        System.out.println("[SUCCESS] [替代成功] 方法: " + method + " -> " + result);
                        saveSuccessResult(method, result);
                    }
                    
                } catch (Exception e) {
                    System.out.println("[替代] 方法异常: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.out.println("[替代方法] 失败: " + e.getMessage());
        }
    }
    
    // 保存成功结果
    private void saveSuccessResult(String param, String result) {
        try {
            String timestamp = new Date().toString();
            String successInfo = String.format(
                "\n=== 签名突破成功 ===\n" +
                "时间: %s\n" +
                "参数: %s\n" +
                "签名: %s\n" +
                "长度: %d\n" +
                "==================\n",
                timestamp, param, result, result.length()
            );
            
            System.out.println(successInfo);
            
            // 这里可以保存到文件
            System.setProperty("cbg.sign.success", result);
            System.setProperty("cbg.sign.param", param);
            
        } catch (Exception e) {
            System.out.println("保存结果失败: " + e.getMessage());
        }
    }
    
    // MD5哈希函数
    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            return input;
        }
    }
}
