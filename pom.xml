<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ying</groupId>
    <artifactId>menghuan-reverse</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <repositories>
        <repository>
            <id>jitpack.io</id>
            <url>https://jitpack.io</url>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>com.github.zhkl0228</groupId>
            <artifactId>unidbg-android</artifactId>
            <version>0.9.7</version>
        </dependency>
        <dependency>
            <groupId>com.github.zhkl0228</groupId>
            <artifactId>unidbg-api</artifactId>
            <version>0.9.7</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <mainClass>com.ying.menghuan.Menghuan</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <id>run-main</id>
                        <configuration>
                            <mainClass>com.ying.menghuan.Menghuan</mainClass>
                        </configuration>
                    </execution>
                    <execution>
                        <id>run-advanced</id>
                        <configuration>
                            <mainClass>com.ying.menghuan.AdvancedSignatureAnalyzer</mainClass>
                        </configuration>
                    </execution>
                    <execution>
                        <id>run-tracer</id>
                        <configuration>
                            <mainClass>com.ying.menghuan.MemoryInstructionTracer</mainClass>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
