package com.ying.menghuan;

import java.io.*;
import java.util.zip.*;
import java.security.cert.*;
import java.security.MessageDigest;
import java.util.Collection;

public class APKCertExtractor {
    
    public static void main(String[] args) {
        System.out.println("============================================================");
        System.out.println("    梦幻藏宝阁 APK 证书提取和分析工具");
        System.out.println("============================================================");
        
        String apkPath = "apks/menghuan/v5.81.0.apk";
        
        try {
            APKCertExtractor extractor = new APKCertExtractor();
            extractor.extractAndAnalyzeCertificate(apkPath);
        } catch (Exception e) {
            System.out.println("[ERROR] 证书提取失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public void extractAndAnalyzeCertificate(String apkPath) throws Exception {
        System.out.println("[*] 开始提取APK证书...");
        
        File apkFile = new File(apkPath);
        if (!apkFile.exists()) {
            throw new FileNotFoundException("APK文件不存在: " + apkPath);
        }
        
        try (ZipFile zipFile = new ZipFile(apkFile)) {
            // 查找证书文件
            ZipEntry certEntry = findCertificateEntry(zipFile);
            if (certEntry == null) {
                System.out.println("[ERROR] 未找到证书文件");
                return;
            }
            
            System.out.println("[*] 找到证书文件: " + certEntry.getName());
            System.out.println("[*] 证书文件大小: " + certEntry.getSize() + " bytes");
            
            // 提取证书文件
            byte[] certData = extractCertificateData(zipFile, certEntry);
            
            // 保存证书文件
            saveCertificateFile(certData, certEntry.getName());
            
            // 分析证书内容
            analyzeCertificate(certData);
            
            // 计算证书哈希
            calculateCertificateHashes(certData);
            
        }
    }
    
    private ZipEntry findCertificateEntry(ZipFile zipFile) {
        // 查找META-INF目录下的证书文件
        String[] certExtensions = {".RSA", ".DSA", ".EC"};
        
        for (ZipEntry entry : zipFile.stream().toArray(ZipEntry[]::new)) {
            String name = entry.getName().toUpperCase();
            if (name.startsWith("META-INF/")) {
                for (String ext : certExtensions) {
                    if (name.endsWith(ext)) {
                        return entry;
                    }
                }
            }
        }
        return null;
    }
    
    private byte[] extractCertificateData(ZipFile zipFile, ZipEntry certEntry) throws IOException {
        try (InputStream is = zipFile.getInputStream(certEntry);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            
            return baos.toByteArray();
        }
    }
    
    private void saveCertificateFile(byte[] certData, String originalName) throws IOException {
        String fileName = originalName.substring(originalName.lastIndexOf('/') + 1);
        
        try (FileOutputStream fos = new FileOutputStream(fileName)) {
            fos.write(certData);
            System.out.println("[*] 证书文件已保存: " + fileName);
        }
    }
    
    private void analyzeCertificate(byte[] certData) {
        System.out.println("\n[*] ========== 证书内容分析 ==========");
        
        try {
            // 尝试解析PKCS#7格式
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            
            // PKCS#7格式通常包含多个证书
            Collection<? extends Certificate> certificates = cf.generateCertificates(
                new ByteArrayInputStream(certData)
            );
            
            if (certificates.isEmpty()) {
                System.out.println("[ERROR] 无法解析证书内容");
                return;
            }
            
            System.out.println("[*] 找到 " + certificates.size() + " 个证书");
            
            int index = 1;
            for (Certificate cert : certificates) {
                if (cert instanceof X509Certificate) {
                    analyzeX509Certificate((X509Certificate) cert, index++);
                }
            }
            
        } catch (Exception e) {
            System.out.println("[ERROR] 证书解析失败: " + e.getMessage());
            
            // 尝试十六进制分析
            analyzeHexContent(certData);
        }
    }
    
    private void analyzeX509Certificate(X509Certificate cert, int index) {
        System.out.println("\n--- 证书 " + index + " ---");
        System.out.println("颁发者: " + cert.getIssuerDN());
        System.out.println("主题: " + cert.getSubjectDN());
        System.out.println("序列号: " + cert.getSerialNumber());
        System.out.println("有效期从: " + cert.getNotBefore());
        System.out.println("有效期到: " + cert.getNotAfter());
        System.out.println("签名算法: " + cert.getSigAlgName());
        System.out.println("版本: " + cert.getVersion());
        
        // 检查是否是NetEase证书
        String issuer = cert.getIssuerDN().toString().toLowerCase();
        String subject = cert.getSubjectDN().toString().toLowerCase();
        
        if (issuer.contains("netease") || subject.contains("netease")) {
            System.out.println("[OK] 这是NetEase官方证书");
        } else {
            System.out.println("[WARNING] 这不是NetEase官方证书");
        }
        
        // 计算证书指纹
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            MessageDigest sha1 = MessageDigest.getInstance("SHA-1");
            MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
            
            byte[] certBytes = cert.getEncoded();
            
            System.out.println("MD5指纹: " + bytesToHex(md5.digest(certBytes)));
            System.out.println("SHA1指纹: " + bytesToHex(sha1.digest(certBytes)));
            System.out.println("SHA256指纹: " + bytesToHex(sha256.digest(certBytes)));
            
        } catch (Exception e) {
            System.out.println("[ERROR] 指纹计算失败: " + e.getMessage());
        }
    }
    
    private void analyzeHexContent(byte[] certData) {
        System.out.println("\n[*] ========== 十六进制内容分析 ==========");
        System.out.println("证书数据长度: " + certData.length + " bytes");
        
        // 显示前64字节的十六进制内容
        int displayLength = Math.min(64, certData.length);
        System.out.print("前" + displayLength + "字节内容: ");
        for (int i = 0; i < displayLength; i++) {
            System.out.printf("%02X ", certData[i]);
            if ((i + 1) % 16 == 0) System.out.println();
        }
        System.out.println();
        
        // 查找常见的证书标识
        String hexString = bytesToHex(certData);
        
        if (hexString.contains("308")) {
            System.out.println("[OK] 发现ASN.1 DER编码标识");
        }

        if (hexString.contains("06092A864886F70D010701")) {
            System.out.println("[OK] 发现PKCS#7 signedData标识");
        }
        
        if (hexString.contains("06092A864886F70D010702")) {
            System.out.println("[OK] 发现PKCS#7 envelopedData标识");
        }
    }
    
    private void calculateCertificateHashes(byte[] certData) {
        System.out.println("\n[*] ========== 证书文件哈希 ==========");
        
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            MessageDigest sha1 = MessageDigest.getInstance("SHA-1");
            MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
            
            System.out.println("MD5:    " + bytesToHex(md5.digest(certData)));
            System.out.println("SHA1:   " + bytesToHex(sha1.digest(certData)));
            System.out.println("SHA256: " + bytesToHex(sha256.digest(certData)));
            
        } catch (Exception e) {
            System.out.println("[ERROR] 哈希计算失败: " + e.getMessage());
        }
    }
    
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
}
