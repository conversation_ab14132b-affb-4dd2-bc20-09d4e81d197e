package com.ying.menghuan;

import com.github.unidbg.AndroidEmulator;
import com.github.unidbg.Module;
import com.github.unidbg.linux.android.AndroidEmulatorBuilder;
import com.github.unidbg.linux.android.AndroidResolver;
import com.github.unidbg.linux.android.dvm.*;
import com.github.unidbg.linux.android.dvm.api.ApplicationInfo;
import com.github.unidbg.memory.Memory;
import com.github.unidbg.virtualmodule.android.AndroidModule;
import com.github.unidbg.debugger.Debugger;
import com.github.unidbg.pointer.UnidbgPointer;

import java.io.File;
import java.util.*;

/**
 * 梦幻藏宝阁 cbg-auth-sign 高级签名分析器
 * 专门针对0x162f4-0x163a8之间180字节验证逻辑的深度分析
 */
public class AdvancedSignatureAnalyzer extends AbstractJni {
    public static AndroidEmulator emulator;
    public static Memory memory;
    public static VM vm;
    public static Module module;
    
    // 关键地址
    private static final long SIGN_METHOD_ADDR = 0x162f4L;
    private static final long FAIL_RETURN_ADDR = 0x163a8L;
    private static final long VERIFICATION_SIZE = FAIL_RETURN_ADDR - SIGN_METHOD_ADDR;
    
    // 分析结果存储
    private List<String> executionTrace = new ArrayList<>();
    private Map<String, Object> analysisResults = new HashMap<>();
    
    public AdvancedSignatureAnalyzer() {
        System.out.println("[*] ========================================");
        System.out.println("[*]    梦幻藏宝阁 cbg-auth-sign 高级分析器");
        System.out.println("[*] ========================================");
        
        emulator = AndroidEmulatorBuilder.for64Bit().setProcessName("com.netease.xyqcbg").build();
        memory = emulator.getMemory();
        memory.setLibraryResolver(new AndroidResolver(23));
        vm = emulator.createDalvikVM(new File("apks/menghuan/v5.81.0.apk"));
        vm.setJni(this);
        new AndroidModule(emulator, vm).register(memory);
        
        // 启用详细日志
        vm.setVerbose(true);
        
        DalvikModule dm = vm.loadLibrary("netseckit-4.2.3", true);
        module = dm.getModule();
        
        System.out.println("[*] 库加载完成，基址: 0x" + Long.toHexString(module.base));
        System.out.println("[*] 目标方法地址: 0x" + Long.toHexString(module.base + SIGN_METHOD_ADDR));
        System.out.println("[*] 失败返回地址: 0x" + Long.toHexString(module.base + FAIL_RETURN_ADDR));
        System.out.println("[*] 验证逻辑大小: " + VERIFICATION_SIZE + " 字节");
        
        dm.callJNI_OnLoad(emulator);
        
        // 设置调试断点
        setupDebugBreakpoints();
    }
    
    /**
     * 设置调试断点来监控验证逻辑
     */
    private void setupDebugBreakpoints() {
        System.out.println("[*] 设置调试断点...");
        
        Debugger debugger = emulator.attach();
        
        // 在签名方法入口设置断点
        debugger.addBreakPoint(module.base + SIGN_METHOD_ADDR, new Debugger.BreakPointCallback() {
            @Override
            public boolean onHit(Emulator<?> emulator, long address) {
                System.out.println("[断点] 进入签名方法: 0x" + Long.toHexString(address));
                executionTrace.add("ENTER_SIGN_METHOD: 0x" + Long.toHexString(address));
                return true; // 继续执行
            }
        });
        
        // 在失败返回点设置断点
        debugger.addBreakPoint(module.base + FAIL_RETURN_ADDR, new Debugger.BreakPointCallback() {
            @Override
            public boolean onHit(Emulator<?> emulator, long address) {
                System.out.println("[断点] 到达失败返回点: 0x" + Long.toHexString(address));
                executionTrace.add("REACH_FAIL_RETURN: 0x" + Long.toHexString(address));
                
                // 分析此时的寄存器状态
                analyzeRegistersAtFailure();
                return true;
            }
        });
        
        // 在验证逻辑中间设置多个断点
        for (long offset = SIGN_METHOD_ADDR + 0x10; offset < FAIL_RETURN_ADDR; offset += 0x20) {
            final long currentAddr = offset;
            debugger.addBreakPoint(module.base + offset, new Debugger.BreakPointCallback() {
                @Override
                public boolean onHit(Emulator<?> emulator, long address) {
                    System.out.println("[断点] 验证逻辑中间点: 0x" + Long.toHexString(address));
                    executionTrace.add("VERIFICATION_STEP: 0x" + Long.toHexString(address));
                    return true;
                }
            });
        }
        
        System.out.println("[*] 调试断点设置完成");
    }
    
    /**
     * 分析失败时的寄存器状态
     */
    private void analyzeRegistersAtFailure() {
        System.out.println("[分析] 失败时寄存器状态:");
        // 这里可以添加寄存器分析逻辑
        // 由于unidbg的限制，我们主要通过日志来分析
    }
    
    /**
     * 执行签名分析
     */
    public void analyzeSignature() {
        System.out.println("\n[*] 开始签名分析...");
        
        // 测试不同的参数组合
        String[] testParams = {
            "48de41784dad6ff20e42640f2d5b0151", // 原始参数
            String.valueOf(System.currentTimeMillis()), // 当前时间戳
            String.valueOf(System.currentTimeMillis() / 1000), // 秒级时间戳
            "com.netease.xyqcbg", // 包名
            "netseckit-4.2.3", // 库名
            "0dbfed16914b56294b8e162cafcc1ff1", // APK MD5
            "9c55391bfac316e6c5d7f758d8de4500746da5bc", // APK SHA1
            generateRandomHex(32), // 随机32位十六进制
            generateRandomHex(64), // 随机64位十六进制
        };
        
        for (int i = 0; i < testParams.length; i++) {
            String param = testParams[i];
            System.out.println("\n[测试 " + (i + 1) + "/" + testParams.length + "] 参数: " + param);
            
            executionTrace.clear();
            String result = callSignatureMethod(param);
            
            System.out.println("[结果] " + (result.isEmpty() ? "失败 (空字符串)" : "成功: " + result));
            
            // 保存分析结果
            Map<String, Object> testResult = new HashMap<>();
            testResult.put("param", param);
            testResult.put("result", result);
            testResult.put("success", !result.isEmpty());
            testResult.put("trace", new ArrayList<>(executionTrace));
            
            analysisResults.put("test_" + i, testResult);
            
            if (!result.isEmpty()) {
                System.out.println("[SUCCESS] 发现有效签名!");
                break;
            }
        }
        
        // 输出分析总结
        printAnalysisSummary();
    }
    
    /**
     * 调用签名方法
     */
    private String callSignatureMethod(String param) {
        try {
            DvmClass class_JNIFactory = vm.resolveClass("com/netease/NetSecKit/factory/JNIFactory");
            String method_w3270a03dafee4a0a = "w3270a03dafee4a0a(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;";

            DvmObject<?> obj = class_JNIFactory.callStaticJniMethodObject(
                    emulator,
                    method_w3270a03dafee4a0a,
                    vm.resolveClass("android/content/Context").newObject(null),
                    param
            );
            
            return obj.getValue().toString();
        } catch (Exception e) {
            System.out.println("[错误] 调用签名方法失败: " + e.getMessage());
            return "";
        }
    }
    
    /**
     * 生成随机十六进制字符串
     */
    private String generateRandomHex(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(Integer.toHexString(random.nextInt(16)));
        }
        return sb.toString();
    }
    
    /**
     * 输出分析总结
     */
    private void printAnalysisSummary() {
        System.out.println("\n[*] ========== 分析总结 ==========");
        System.out.println("[*] 测试参数数量: " + analysisResults.size());
        
        long successCount = analysisResults.values().stream()
                .mapToLong(result -> (Boolean) ((Map<?, ?>) result).get("success") ? 1 : 0)
                .sum();
        
        System.out.println("[*] 成功次数: " + successCount);
        System.out.println("[*] 失败次数: " + (analysisResults.size() - successCount));
        
        if (successCount == 0) {
            System.out.println("\n[分析] 所有测试都失败，问题确认在验证逻辑中");
            System.out.println("[建议] 需要进行静态分析来理解验证条件");
            System.out.println("[建议] 使用IDA Pro分析地址范围: 0x" + 
                Long.toHexString(module.base + SIGN_METHOD_ADDR) + " - 0x" + 
                Long.toHexString(module.base + FAIL_RETURN_ADDR));
        }
        
        System.out.println("\n[*] 执行轨迹示例:");
        if (!executionTrace.isEmpty()) {
            executionTrace.forEach(trace -> System.out.println("  " + trace));
        }
    }
    
    // JNI环境补全方法（与原版本相同）
    @Override
    public int getStaticIntField(BaseVM vm, DvmClass dvmClass, String signature) {
        switch (signature) {
            case "com/netease/NetSecKit/poly/a->o:I":
                return 2;
            case "com/netease/NetSecKit/poly/a->p:I":
                return 12;
        }
        return super.getStaticIntField(vm, dvmClass, signature);
    }

    @Override
    public DvmObject<?> callObjectMethodV(BaseVM vm, DvmObject<?> dvmObject, String signature, VaList vaList) {
        switch (signature) {
            case "android/content/Context->getApplicationInfo()Landroid/content/pm/ApplicationInfo;":
                return vm.resolveClass("android/content/pm/ApplicationInfo").newObject(null);
        }
        return super.callObjectMethodV(vm, dvmObject, signature, vaList);
    }

    @Override
    public DvmObject<?> getObjectField(BaseVM vm, DvmObject<?> dvmObject, String signature) {
        switch (signature) {
            case "android/content/pm/ApplicationInfo->publicSourceDir:Ljava/lang/String;":
                return new StringObject(vm, "/data/app/com.netease.xyqcbg/base.apk");
        }
        return super.getObjectField(vm, dvmObject, signature);
    }
    
    public static void main(String[] args) {
        AdvancedSignatureAnalyzer analyzer = new AdvancedSignatureAnalyzer();
        analyzer.analyzeSignature();
    }
}
