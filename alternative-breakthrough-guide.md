# 梦幻藏宝阁 cbg-auth-sign 替代突破方案指南

## 方案概述

由于暂时不使用IDA Pro进行静态分析，我们提供了4个高效的替代突破方案，每个方案都有不同的技术路径和适用场景。

## 方案选择建议

### 方案A: Frida动态Hook (最推荐)
- **适用场景**: 有Android设备或模拟器
- **技术难度**: 3星
- **成功概率**: 5星
- **获取信息**: 真实签名值、调用参数、执行流程

### 方案B: 网络抓包分析 (次推荐)
- **适用场景**: 可以运行真实应用
- **技术难度**: 2星
- **成功概率**: 4星
- **获取信息**: HTTP请求中的签名值、API调用模式

### 方案C: 增强unidbg环境 (备选)
- **适用场景**: 继续优化现有环境
- **技术难度**: 4星
- **成功概率**: 3星
- **获取信息**: 更完善的模拟环境

### 方案D: 混合分析法 (高级)
- **适用场景**: 结合多种方法
- **技术难度**: 5星
- **成功概率**: 5星
- **获取信息**: 全方位的技术情报

## 方案A: Frida动态Hook 详细操作

### 环境准备

1. **安装Frida**
```bash
# 安装Frida工具
pip install frida-tools

# 下载frida-server (根据设备架构选择)
# ARM64: https://github.com/frida/frida/releases
wget https://github.com/frida/frida/releases/download/16.1.4/frida-server-16.1.4-android-arm64.xz
```

2. **设备配置**
```bash
# 解压并推送到设备
unxz frida-server-16.1.4-android-arm64.xz
adb push frida-server-16.1.4-android-arm64 /data/local/tmp/frida-server
adb shell chmod 755 /data/local/tmp/frida-server

# 启动frida-server (需要root权限)
adb shell su -c "/data/local/tmp/frida-server &"
```

### 执行Hook

```bash
# 使用我们提供的Hook脚本
frida -U -f com.netease.xyqcbg -l frida_hook_cbg.js --no-pause

# 或者Hook已运行的进程
frida -U com.netease.xyqcbg -l frida_hook_cbg.js
```

### 预期结果

Hook脚本会捕获到：
- 所有JNI签名方法的调用
- 输入参数和返回值
- HTTP请求中的签名Header
- 成功的签名会保存到 `/sdcard/cbg_sign_success.log`

## 方案B: 网络抓包分析 详细操作

### 环境准备

1. **安装mitmproxy**
```bash
pip install mitmproxy
```

2. **配置设备代理**
```bash
# 启动mitmproxy
mitmproxy -s capture_cbg_traffic.py --set confdir=~/.mitmproxy

# 设置手机WiFi代理指向电脑IP:8080
# 安装mitmproxy证书到手机
```

### 执行抓包

1. **启动抓包脚本**
```bash
mitmproxy -s capture_cbg_traffic.py
```

2. **操作应用**
- 打开梦幻藏宝阁应用
- 进行登录、浏览商品等操作
- 触发API请求

### 预期结果

抓包脚本会分析：
- 所有CBG相关的HTTP/HTTPS请求
- 请求头中的cbg-auth-sign值
- 请求体和URL参数中的签名
- 自动保存到 `cbg_requests.json`

## 方案C: 增强unidbg环境

### 当前改进

我们已经对unidbg环境进行了以下增强：

1. **真实APK环境模拟**
   - PackageManager和PackageInfo模拟
   - 应用签名信息模拟
   - 包名和版本信息设置

2. **真实设备环境模拟**
   - Android系统属性设置
   - 设备指纹信息模拟
   - 网络环境配置

3. **证书验证环境**
   - MessageDigest和CertificateFactory模拟
   - APK签名验证流程模拟

### 运行增强版本

```bash
cd g:\YMSJ\mh
mvn compile exec:java -q
```

## 方案D: 混合分析法

### 组合策略

1. **第一阶段: 网络抓包**
   - 获取真实的cbg-auth-sign值
   - 分析签名的格式和长度
   - 确定签名的使用场景

2. **第二阶段: Frida Hook**
   - Hook签名生成方法
   - 获取生成签名的输入参数
   - 分析签名算法的调用流程

3. **第三阶段: unidbg优化**
   - 根据前两阶段的发现优化环境
   - 补全缺失的JNI方法和环境信息
   - 实现完整的签名生成

## 预期突破时间线

### 快速突破 (1-2天)
- **方案A**: Frida Hook获取真实签名值
- **方案B**: 网络抓包分析签名格式

### 深度分析 (3-5天)  
- **方案C**: 完善unidbg环境模拟
- **方案D**: 混合分析获得完整算法

### 完整实现 (1周)
- 基于分析结果实现独立的签名算法
- 验证算法的正确性和稳定性
- 编写完整的技术文档

## 关键突破点

基于我们已有的分析，重点关注：

1. **APK文件验证**
   - 文件完整性检查
   - 数字签名验证
   - 证书链验证

2. **设备环境验证**
   - 设备指纹匹配
   - 系统属性检查
   - 硬件信息验证

3. **时间和随机性**
   - 时间戳验证
   - 随机数生成
   - 防重放机制

## 项目文件说明

- `frida_hook_cbg.js`: Frida Hook脚本，用于动态分析
- `capture_cbg_traffic.py`: 网络抓包分析脚本
- `src/main/java/com/xiaofeng/menghuan/Menghuan.java`: 增强版unidbg逆向代码
- `final-reverse-analysis-report.md`: 详细的技术分析报告

## 立即行动建议

1. **优先选择方案A (Frida Hook)**
   - 准备Android设备或模拟器
   - 安装Frida环境
   - 运行Hook脚本获取真实签名

2. **同时进行方案B (网络抓包)**
   - 配置mitmproxy环境
   - 抓取真实的API请求
   - 分析签名的使用模式

3. **根据发现优化方案C**
   - 基于前两个方案的发现
   - 继续完善unidbg环境
   - 实现完整的签名算法

这些方案提供了多个技术路径，可以根据你的具体环境和技术偏好选择最适合的方案进行突破。
