#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Emoji替换工具
用于批量替换代码和文档中的emoji表情为纯文本格式
"""

import os
import re
import argparse
from pathlib import Path
from typing import Dict, List, Tuple

class EmojiReplacer:
    """Emoji替换器"""
    
    def __init__(self):
        # 定义emoji替换映射表
        self.emoji_mappings = {
            # 状态指示符
            '✅': '[OK]',
            '❌': '[ERROR]', 
            '⚠️': '[WARNING]',
            '🔴': '[ERROR]',
            '🟢': '[OK]',
            '🟡': '[WARNING]',
            
            # 功能标识符
            '🔑': '[KEY]',
            '📊': '[STATS]',
            '🎯': '[TARGET]',
            '🔍': '[SEARCH]',
            '📱': '[MOBILE]',
            '💻': '[COMPUTER]',
            '🌐': '[NETWORK]',
            '📁': '[FOLDER]',
            '📄': '[FILE]',
            '🔧': '[TOOL]',
            '⚙️': '[CONFIG]',
            '🛠️': '[TOOLS]',
            
            # 动作指示符
            '🚀': '[EXECUTE]',
            '▶️': '[PLAY]',
            '⏸️': '[PAUSE]',
            '⏹️': '[STOP]',
            '🔄': '[REFRESH]',
            '💾': '[SAVE]',
            '📤': '[UPLOAD]',
            '📥': '[DOWNLOAD]',
            '🔗': '[LINK]',
            '📋': '[COPY]',
            
            # 等级和评分
            '⭐': '*',
            '🥇': '[FIRST]',
            '🥈': '[SECOND]', 
            '🥉': '[THIRD]',
            '🏆': '[WINNER]',
            
            # 情感表达
            '🎉': '[SUCCESS]',
            '👍': '[GOOD]',
            '👎': '[BAD]',
            '💡': '[IDEA]',
            '⚡': '[FAST]',
            '🔥': '[HOT]',
            '❄️': '[COLD]',
            
            # 方向指示
            '➡️': '->',
            '⬅️': '<-',
            '⬆️': '^',
            '⬇️': 'v',
            '↗️': '/',
            '↘️': '\\',
            
            # 其他常用emoji
            '📝': '[NOTE]',
            '📌': '[PIN]',
            '🔖': '[BOOKMARK]',
            '🏷️': '[TAG]',
            '📍': '[LOCATION]',
            '🎨': '[DESIGN]',
            '🔒': '[LOCK]',
            '🔓': '[UNLOCK]',
            '🆕': '[NEW]',
            '🆙': '[UP]',
            '🔜': '[SOON]',
            '✨': '[SPARKLE]',
            '💫': '[STAR]',
            '🌟': '[BRIGHT]',
        }
        
        # 支持的文件扩展名
        self.supported_extensions = {
            '.py', '.java', '.js', '.ts', '.md', '.txt', '.json', 
            '.xml', '.html', '.css', '.scss', '.less', '.yml', '.yaml',
            '.sh', '.bat', '.ps1', '.sql', '.php', '.rb', '.go', '.rs',
            '.cpp', '.c', '.h', '.hpp', '.cs', '.vb', '.swift', '.kt'
        }
    
    def replace_emojis_in_text(self, text: str) -> Tuple[str, int]:
        """
        替换文本中的emoji
        
        Args:
            text: 原始文本
            
        Returns:
            tuple: (替换后的文本, 替换次数)
        """
        replaced_text = text
        total_replacements = 0
        
        for emoji, replacement in self.emoji_mappings.items():
            count = replaced_text.count(emoji)
            if count > 0:
                replaced_text = replaced_text.replace(emoji, replacement)
                total_replacements += count
        
        return replaced_text, total_replacements
    
    def should_skip_file(self, file_path: Path) -> bool:
        """
        判断是否应该跳过文件

        Args:
            file_path: 文件路径

        Returns:
            bool: 是否跳过
        """
        # 跳过自己
        if file_path.name == 'emoji_replacer.py':
            return True

        # 跳过其他可能包含emoji映射的配置文件
        skip_patterns = [
            'emoji_config',
            'emoji_mapping',
            'emoji_dict'
        ]

        for pattern in skip_patterns:
            if pattern in file_path.name.lower():
                return True

        return False

    def process_file(self, file_path: Path) -> Tuple[bool, int]:
        """
        处理单个文件

        Args:
            file_path: 文件路径

        Returns:
            tuple: (是否有修改, 替换次数)
        """
        # 检查是否应该跳过
        if self.should_skip_file(file_path):
            return False, 0

        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            # 替换emoji
            new_content, replacements = self.replace_emojis_in_text(original_content)

            # 如果有替换，写回文件
            if replacements > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                return True, replacements

            return False, 0

        except Exception as e:
            print(f"[ERROR] 处理文件失败 {file_path}: {e}")
            return False, 0
    
    def scan_directory(self, directory: Path, recursive: bool = True) -> List[Path]:
        """
        扫描目录中的支持文件
        
        Args:
            directory: 目录路径
            recursive: 是否递归扫描
            
        Returns:
            list: 文件路径列表
        """
        files = []
        
        if recursive:
            for file_path in directory.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in self.supported_extensions:
                    files.append(file_path)
        else:
            for file_path in directory.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in self.supported_extensions:
                    files.append(file_path)
        
        return files
    
    def process_directory(self, directory: Path, recursive: bool = True) -> Dict[str, int]:
        """
        处理目录中的所有文件
        
        Args:
            directory: 目录路径
            recursive: 是否递归处理
            
        Returns:
            dict: 处理结果统计
        """
        files = self.scan_directory(directory, recursive)
        
        stats = {
            'total_files': len(files),
            'modified_files': 0,
            'total_replacements': 0,
            'processed_files': []
        }
        
        for file_path in files:
            print(f"[INFO] 处理文件: {file_path}")

            # 检查是否跳过
            if self.should_skip_file(file_path):
                print(f"[SKIP] 跳过文件 (排除列表)")
                continue

            modified, replacements = self.process_file(file_path)

            if modified:
                stats['modified_files'] += 1
                stats['total_replacements'] += replacements
                stats['processed_files'].append({
                    'file': str(file_path),
                    'replacements': replacements
                })
                print(f"[OK] 替换了 {replacements} 个emoji")
            else:
                print(f"[SKIP] 无需修改")
        
        return stats
    
    def preview_file(self, file_path: Path) -> List[Tuple[str, str, int]]:
        """
        预览文件中的emoji替换

        Args:
            file_path: 文件路径

        Returns:
            list: [(原emoji, 替换文本, 出现次数)]
        """
        # 检查是否应该跳过
        if self.should_skip_file(file_path):
            return []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            preview = []
            for emoji, replacement in self.emoji_mappings.items():
                count = content.count(emoji)
                if count > 0:
                    preview.append((emoji, replacement, count))

            return preview

        except Exception as e:
            print(f"[ERROR] 预览文件失败 {file_path}: {e}")
            return []


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Emoji替换工具')
    parser.add_argument('path', help='文件或目录路径')
    parser.add_argument('--preview', '-p', action='store_true', help='预览模式，不实际修改文件')
    parser.add_argument('--recursive', '-r', action='store_true', default=True, help='递归处理子目录')
    parser.add_argument('--no-recursive', action='store_true', help='不递归处理子目录')
    
    args = parser.parse_args()
    
    # 处理递归参数
    recursive = args.recursive and not args.no_recursive
    
    path = Path(args.path)
    replacer = EmojiReplacer()
    
    if not path.exists():
        print(f"[ERROR] 路径不存在: {path}")
        return
    
    if path.is_file():
        # 处理单个文件
        if args.preview:
            preview = replacer.preview_file(path)
            if preview:
                print(f"[INFO] 文件 {path} 中发现的emoji:")
                for emoji, replacement, count in preview:
                    print(f"  {emoji} -> {replacement} (出现 {count} 次)")
            else:
                print(f"[INFO] 文件 {path} 中未发现emoji")
        else:
            modified, replacements = replacer.process_file(path)
            if modified:
                print(f"[OK] 成功替换 {replacements} 个emoji")
            else:
                print(f"[INFO] 文件无需修改")
    
    elif path.is_dir():
        # 处理目录
        if args.preview:
            files = replacer.scan_directory(path, recursive)
            print(f"[INFO] 扫描到 {len(files)} 个支持的文件")
            
            total_emojis = 0
            for file_path in files:
                preview = replacer.preview_file(file_path)
                if preview:
                    print(f"\n[INFO] 文件 {file_path}:")
                    for emoji, replacement, count in preview:
                        print(f"  {emoji} -> {replacement} (出现 {count} 次)")
                        total_emojis += count
            
            print(f"\n[SUMMARY] 总共发现 {total_emojis} 个emoji需要替换")
        else:
            stats = replacer.process_directory(path, recursive)
            
            print(f"\n[SUMMARY] 处理完成:")
            print(f"  扫描文件: {stats['total_files']}")
            print(f"  修改文件: {stats['modified_files']}")
            print(f"  替换emoji: {stats['total_replacements']}")
            
            if stats['processed_files']:
                print(f"\n[DETAILS] 修改的文件:")
                for item in stats['processed_files']:
                    print(f"  {item['file']}: {item['replacements']} 个替换")


if __name__ == '__main__':
    main()
