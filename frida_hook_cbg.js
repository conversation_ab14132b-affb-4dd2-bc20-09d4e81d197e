// 梦幻藏宝阁 cbg-auth-sign 参数 Frida Hook 脚本
// 使用方法: frida -U -f com.netease.xyqcbg -l frida_hook_cbg.js --no-pause

console.log("[*] 梦幻藏宝阁签名Hook脚本启动...");

Java.perform(function() {
    console.log("[*] Java环境就绪，开始Hook...");
    
    try {
        // Hook JNIFactory类的所有签名方法
        var JNIFactory = Java.use("com.netease.NetSecKit.factory.JNIFactory");
        console.log("[*] 找到JNIFactory类");
        
        // Hook主要的签名方法 w3270a03dafee4a0a
        if (JNIFactory.w3270a03dafee4a0a) {
            JNIFactory.w3270a03dafee4a0a.implementation = function(context, param) {
                console.log("\n[Hook] ========== w3270a03dafee4a0a 调用 ==========");
                console.log("[Hook] 参数1 (Context):", context);
                console.log("[Hook] 参数2 (String):", param);
                console.log("[Hook] 参数2长度:", param.length);
                console.log("[Hook] 参数2十六进制:", stringToHex(param));
                
                // 记录调用栈
                console.log("[Hook] 调用栈:");
                console.log(Java.use("android.util.Log").getStackTraceString(Java.use("java.lang.Exception").$new()));
                
                // 调用原方法
                var result = this.w3270a03dafee4a0a(context, param);
                
                console.log("[Hook] 返回结果:", result);
                console.log("[Hook] 结果长度:", result.length);
                if (result.length > 0) {
                    console.log("[Hook] 结果十六进制:", stringToHex(result));
                    console.log("[Hook] [OK] 成功获取签名!");
                    
                    // 保存成功的参数和结果
                    saveSuccessfulSign(param, result);
                } else {
                    console.log("[Hook] [ERROR] 返回空字符串");
                }
                
                return result;
            };
            console.log("[*] w3270a03dafee4a0a Hook完成");
        }
        
        // Hook其他签名方法
        var methods = [
            "w1228bcedf6204eeb",
            "w183a9e5310205b79", 
            "w92bbe0960670a111",
            "w534b93edf14c9e3b",
            "we3f5598dde26215d",
            "w7aa090cda25562b5",
            "w01c166e77807dbf0",
            "w734672804aee66ac"
        ];
        
        methods.forEach(function(methodName) {
            try {
                if (JNIFactory[methodName]) {
                    JNIFactory[methodName].implementation = function(context, param) {
                        console.log("\n[Hook] " + methodName + " 调用");
                        console.log("[Hook] 参数:", param);
                        var result = this[methodName](context, param);
                        console.log("[Hook] 返回:", result);
                        if (result && result.length > 0) {
                            console.log("[Hook] [OK] " + methodName + " 返回非空结果!");
                            saveSuccessfulSign(param, result, methodName);
                        }
                        return result;
                    };
                    console.log("[*] " + methodName + " Hook完成");
                }
            } catch(e) {
                console.log("[Hook] " + methodName + " Hook失败:", e);
            }
        });
        
    } catch(e) {
        console.log("[Hook] JNIFactory Hook失败:", e);
    }
    
    // Hook Context相关方法
    try {
        var Context = Java.use("android.content.Context");
        Context.getApplicationInfo.implementation = function() {
            console.log("[Hook] Context.getApplicationInfo() 调用");
            var result = this.getApplicationInfo();
            console.log("[Hook] ApplicationInfo:", result);
            return result;
        };
        console.log("[*] Context Hook完成");
    } catch(e) {
        console.log("[Hook] Context Hook失败:", e);
    }
    
    // Hook PackageManager
    try {
        var PackageManager = Java.use("android.content.pm.PackageManager");
        PackageManager.getPackageInfo.overload('java.lang.String', 'int').implementation = function(packageName, flags) {
            console.log("[Hook] PackageManager.getPackageInfo():", packageName, flags);
            var result = this.getPackageInfo(packageName, flags);
            console.log("[Hook] PackageInfo:", result);
            return result;
        };
        console.log("[*] PackageManager Hook完成");
    } catch(e) {
        console.log("[Hook] PackageManager Hook失败:", e);
    }
    
    // Hook网络请求，查找cbg-auth-sign的使用
    try {
        var OkHttpClient = Java.use("okhttp3.OkHttpClient");
        var Request = Java.use("okhttp3.Request");
        
        // Hook Request.Builder.addHeader
        var RequestBuilder = Java.use("okhttp3.Request$Builder");
        RequestBuilder.addHeader.implementation = function(name, value) {
            if (name.toLowerCase().includes("cbg") || name.toLowerCase().includes("auth") || name.toLowerCase().includes("sign")) {
                console.log("\n[Hook] ========== HTTP Header ==========");
                console.log("[Hook] Header名称:", name);
                console.log("[Hook] Header值:", value);
                console.log("[Hook] 值长度:", value.length);
                if (value.length > 0) {
                    console.log("[Hook] 值十六进制:", stringToHex(value));
                    console.log("[Hook] [OK] 发现签名Header!");
                }
            }
            return this.addHeader(name, value);
        };
        console.log("[*] HTTP Header Hook完成");
    } catch(e) {
        console.log("[Hook] HTTP Hook失败:", e);
    }
});

// 辅助函数：字符串转十六进制
function stringToHex(str) {
    var hex = '';
    for (var i = 0; i < str.length; i++) {
        hex += str.charCodeAt(i).toString(16).padStart(2, '0') + ' ';
    }
    return hex.trim();
}

// 保存成功的签名结果
function saveSuccessfulSign(param, result, method) {
    method = method || "w3270a03dafee4a0a";
    var timestamp = new Date().toISOString();
    
    console.log("\n[保存] ========== 成功签名记录 ==========");
    console.log("[保存] 时间:", timestamp);
    console.log("[保存] 方法:", method);
    console.log("[保存] 输入参数:", param);
    console.log("[保存] 输出结果:", result);
    console.log("[保存] =====================================");
    
    // 可以将结果写入文件或发送到服务器
    try {
        var File = Java.use("java.io.File");
        var FileWriter = Java.use("java.io.FileWriter");
        var logFile = File.$new("/sdcard/cbg_sign_success.log");
        var writer = FileWriter.$new(logFile, true);
        
        writer.write("时间: " + timestamp + "\n");
        writer.write("方法: " + method + "\n");
        writer.write("参数: " + param + "\n");
        writer.write("结果: " + result + "\n");
        writer.write("=" * 50 + "\n");
        writer.close();
        
        console.log("[保存] 结果已保存到 /sdcard/cbg_sign_success.log");
    } catch(e) {
        console.log("[保存] 文件保存失败:", e);
    }
}

console.log("[*] Hook脚本加载完成，等待方法调用...");
