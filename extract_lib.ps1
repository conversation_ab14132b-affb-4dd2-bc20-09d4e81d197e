Add-Type -AssemblyName System.IO.Compression.FileSystem

$apkPath = "apks/menghuan/v5.81.0.apk"
$outputPath = "apks/menghuan/lib/arm64-v8a/libnetsecsdk.so"

try {
    $zip = [System.IO.Compression.ZipFile]::OpenRead($apkPath)

    Write-Host "APK文件打开成功，查找库文件..."

    $found = $false
    foreach ($entry in $zip.Entries) {
        if ($entry.FullName -eq "lib/arm64-v8a/libnetsecsdk.so") {
            Write-Host "找到目标库文件: $($entry.FullName)"
            Write-Host "文件大小: $($entry.Length) bytes"

            [System.IO.Compression.ZipFileExtensions]::ExtractToFile($entry, $outputPath, $true)
            Write-Host "库文件提取成功: $outputPath"
            $found = $true
            break
        }
    }

    $zip.Dispose()

    # 检查文件是否存在
    if (Test-Path $outputPath) {
        $fileInfo = Get-Item $outputPath
        Write-Host "提取完成，文件大小: $($fileInfo.Length) bytes"
    } elseif (-not $found) {
        Write-Host "库文件未找到，列出APK中的所有lib文件:"

        $zip2 = [System.IO.Compression.ZipFile]::OpenRead($apkPath)
        foreach ($entry in $zip2.Entries) {
            if ($entry.FullName.StartsWith("lib/")) {
                Write-Host "  $($entry.FullName)"
            }
        }
        $zip2.Dispose()
    }

} catch {
    Write-Host "错误: $($_.Exception.Message)"
}
