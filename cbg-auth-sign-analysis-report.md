# 梦幻藏宝阁 cbg-auth-sign 参数逆向分析报告

## 执行概述

本次逆向分析针对梦幻藏宝阁移动应用的 `cbg-auth-sign` 协议头参数进行了深度分析，使用 unidbg 框架模拟 Android 环境，成功加载并调用了 NetEase NetSecKit 安全库。

## 技术环境

- **目标应用**: 梦幻藏宝阁 v5.81.0 (com.netease.xyqcbg)
- **安全库**: netseckit-4.2.3 (libnetsecsdk.so)
- **逆向框架**: unidbg 0.9.7
- **目标方法**: `w3270a03dafee4a0a(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;`

## 分析结果

### [OK] 成功完成的部分

1. **环境搭建**: 成功创建完整的 unidbg Android 模拟环境
2. **库加载**: netseckit-4.2.3 库成功加载到基址 0x40000000
3. **JNI方法注册**: 12个 native 方法全部成功注册
4. **方法调用**: 目标签名方法能够被正常调用
5. **环境模拟**: 实现了完整的 JNI 环境补全，包括：
   - Context 对象创建
   - ApplicationInfo 信息获取
   - UUID 生成和处理
   - APK 文件路径解析
   - 系统服务模拟

### [WARNING] 发现的问题

**核心问题**: 所有签名方法调用都返回空字符串

**详细分析**:
1. **APK文件访问正常**: 能够成功打开 APK 文件 (84MB, 有效ZIP格式)
2. **执行流程完整**: UUID生成 → APK路径获取 → 文件打开 → 返回空值
3. **多方法测试**: 测试了5个不同的签名方法，结果一致
4. **多参数测试**: 尝试了9种不同参数组合，无有效结果

## 关键发现

### JNI调用链分析
```
w3270a03dafee4a0a 方法执行流程:
1. UUID.randomUUID() - 生成随机UUID [OK]
2. UUID.toString() - 转换为字符串 [OK]  
3. String.getBytes("utf-8") - 获取字节数组 [OK]
4. Context.getApplicationInfo() - 获取应用信息 [OK]
5. ApplicationInfo.publicSourceDir - 获取APK路径 [OK]
6. openat(APK文件) - 打开APK文件 [OK]
7. NewStringUTF("") - 返回空字符串 [ERROR]
```

### 可能的失败原因

1. **APK签名验证失败**: 算法可能验证APK的数字签名
2. **缺少关键环境信息**: 需要真实设备的硬件标识
3. **时间戳验证**: 可能需要服务器时间同步
4. **网络验证**: 算法可能需要在线验证
5. **加密密钥缺失**: 固定参数可能不是完整的密钥

## 技术细节

### 已实现的JNI环境补全
- `getStaticIntField`: NetSecKit poly 类静态字段
- `callObjectMethodV`: Context、UUID、String 方法调用
- `getObjectField`: ApplicationInfo 字段获取
- `callIntMethodV`: 整型方法调用
- `callLongMethodV`: 长整型方法调用  
- `callBooleanMethodV`: 布尔型方法调用

### 测试的方法和参数
**方法列表**:
- w3270a03dafee4a0a (主要目标)
- w1228bcedf6204eeb
- w183a9e5310205b79
- w92bbe0960670a111
- w534b93edf14c9e3b
- we3f5598dde26215d

**参数列表**:
- 48de41784dad6ff20e42640f2d5b0151 (原始固定参数)
- 时间戳 (毫秒/秒)
- 包名、库名、版本号等

## 下一步建议

### 1. 深度静态分析
- 使用 IDA Pro 或 Ghidra 分析 libnetsecsdk.so
- 定位签名算法的具体实现逻辑
- 分析失败返回点的条件判断

### 2. 动态调试增强
- 设置更细粒度的断点
- 监控内存读写操作
- 分析算法的输入输出数据流

### 3. 真实环境对比
- 在真实 Android 设备上运行应用
- 抓包分析实际的 cbg-auth-sign 值
- 对比真实环境和模拟环境的差异

### 4. 算法逆向
- 分析 APK 签名验证逻辑
- 研究设备指纹生成算法
- 理解时间戳和随机数的作用

## 结论

本次逆向分析成功建立了完整的 unidbg 分析环境，能够正常加载和调用目标签名方法，但由于签名算法内部的验证机制，当前无法生成有效的 cbg-auth-sign 值。需要进一步的静态分析和真实环境对比来突破这一瓶颈。

项目已为后续深度分析奠定了坚实的基础，所有代码和环境配置都可以重复使用和扩展。

## 深度分析更新 (第二轮)

### 新增分析内容

#### APK文件哈希分析
- **MD5**: `0dbfed16914b56294b8e162cafcc1ff1`
- **SHA1**: `9c55391bfac316e6c5d7f758d8de4500746da5bc`
- **测试结果**: 哈希值作为签名参数仍然返回空字符串

#### 关键发现
1. **执行模式一致**: 所有方法都遵循相同的执行模式：
   - UUID生成 → APK路径获取 → 文件打开 → 返回空值
2. **文件访问正常**: APK文件能够被正常打开和读取
3. **算法内部验证**: 问题出现在native代码内部的验证逻辑

#### 核心问题定位
通过深度分析，确定问题出现在 `openat` 系统调用之后：
```
openat(APK文件) [OK] → 内部验证逻辑 [ERROR] → NewStringUTF("")
```

### 技术突破点分析

#### 1. APK签名验证假设
NetSecKit可能验证APK的数字签名证书：
- 检查签名证书的有效性
- 验证证书与预期的开发者证书匹配
- 当前APK可能缺少正确的签名或证书已过期

#### 2. 设备环境验证假设
算法可能需要特定的设备环境：
- 真实的Android设备ID
- 特定的硬件指纹
- 网络环境验证

#### 3. 时间同步验证假设
可能存在时间戳验证机制：
- 服务器时间同步检查
- 证书有效期验证
- 防重放攻击机制

### 下一步深度分析策略

#### 方案A: 静态分析突破
1. **使用IDA Pro分析libnetsecsdk.so**
   - 定位 `w3270a03dafee4a0a` 函数的汇编代码
   - 分析返回空字符串的条件分支
   - 找到验证失败的具体原因

2. **关键地址分析**
   ```
   w3270a03dafee4a0a: RX@0x400162f4[libnetsecsdk.so]0x162f4
   NewStringUTF(""): RX@0x400163a8[libnetsecsdk.so]0x163a8
   ```

#### 方案B: 动态调试增强
1. **设置断点监控**
   - 在APK文件读取后设置断点
   - 监控内存中的验证逻辑
   - 跟踪条件分支的执行路径

2. **参数注入测试**
   - 修改内存中的验证条件
   - 绕过特定的检查逻辑
   - 强制返回有效签名

#### 方案C: 真实环境对比
1. **真机抓包分析**
   - 在真实Android设备上运行应用
   - 抓取实际的cbg-auth-sign值
   - 对比真实环境和模拟环境的差异

2. **环境差异分析**
   - 设备指纹对比
   - 系统属性对比
   - 网络环境对比

### 技术实现建议

#### 立即可行的方案
1. **使用Frida进行运行时Hook**
   ```javascript
   Java.perform(function() {
       var JNIFactory = Java.use("com.netease.NetSecKit.factory.JNIFactory");
       JNIFactory.w3270a03dafee4a0a.implementation = function(context, param) {
           console.log("参数:", param);
           var result = this.w3270a03dafee4a0a(context, param);
           console.log("结果:", result);
           return result;
       };
   });
   ```

2. **修改unidbg环境模拟更真实的设备**
   - 添加真实的设备属性
   - 模拟真实的系统服务
   - 提供有效的证书链

#### 中期分析方案
1. **反编译APK获取更多信息**
   - 分析Java层的调用逻辑
   - 查找签名生成的上下文
   - 理解参数的真实含义

2. **网络协议分析**
   - 分析完整的API请求流程
   - 理解cbg-auth-sign在协议中的作用
   - 找到其他相关的验证参数

### 结论更新

经过深度分析，确认技术环境已经完全就绪，问题集中在算法内部的验证逻辑。建议优先采用静态分析方案，直接分析native代码的验证条件，这是最有可能快速突破的方向。
