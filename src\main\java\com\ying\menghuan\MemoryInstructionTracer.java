package com.ying.menghuan;

import com.github.unidbg.AndroidEmulator;
import com.github.unidbg.Module;
import com.github.unidbg.linux.android.AndroidEmulatorBuilder;
import com.github.unidbg.linux.android.AndroidResolver;
import com.github.unidbg.linux.android.dvm.*;
import com.github.unidbg.linux.android.dvm.api.ApplicationInfo;
import com.github.unidbg.memory.Memory;
import com.github.unidbg.virtualmodule.android.AndroidModule;
import com.github.unidbg.arm.backend.Backend;
import com.github.unidbg.debugger.Debugger;
import com.github.unidbg.pointer.UnidbgPointer;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;

/**
 * 内存和指令跟踪器
 * 专门用于分析签名验证失败的具体原因
 */
public class MemoryInstructionTracer extends AbstractJni {
    public static AndroidEmulator emulator;
    public static Memory memory;
    public static VM vm;
    public static Module module;
    
    // 关键地址
    private static final long SIGN_METHOD_ADDR = 0x162f4L;
    private static final long FAIL_RETURN_ADDR = 0x163a8L;
    
    // 跟踪数据
    private List<InstructionTrace> instructionTraces = new ArrayList<>();
    private List<MemoryAccess> memoryAccesses = new ArrayList<>();
    private Map<Long, String> addressLabels = new HashMap<>();
    
    // 内部类：指令跟踪
    static class InstructionTrace {
        long address;
        String instruction;
        String registers;
        long timestamp;
        
        InstructionTrace(long address, String instruction, String registers) {
            this.address = address;
            this.instruction = instruction;
            this.registers = registers;
            this.timestamp = System.currentTimeMillis();
        }
        
        @Override
        public String toString() {
            return String.format("0x%x: %s | %s", address, instruction, registers);
        }
    }
    
    // 内部类：内存访问
    static class MemoryAccess {
        long address;
        String type; // READ/WRITE
        long value;
        int size;
        long timestamp;
        
        MemoryAccess(long address, String type, long value, int size) {
            this.address = address;
            this.type = type;
            this.value = value;
            this.size = size;
            this.timestamp = System.currentTimeMillis();
        }
        
        @Override
        public String toString() {
            return String.format("%s 0x%x: 0x%x (%d bytes)", type, address, value, size);
        }
    }
    
    public MemoryInstructionTracer() {
        System.out.println("[*] ========================================");
        System.out.println("[*]      内存指令跟踪器启动");
        System.out.println("[*] ========================================");
        
        emulator = AndroidEmulatorBuilder.for64Bit().setProcessName("com.netease.xyqcbg").build();
        memory = emulator.getMemory();
        memory.setLibraryResolver(new AndroidResolver(23));
        vm = emulator.createDalvikVM(new File("apks/menghuan/v5.81.0.apk"));
        vm.setJni(this);
        new AndroidModule(emulator, vm).register(memory);
        
        DalvikModule dm = vm.loadLibrary("netseckit-4.2.3", true);
        module = dm.getModule();
        
        System.out.println("[*] 库基址: 0x" + Long.toHexString(module.base));
        
        // 初始化地址标签
        initializeAddressLabels();
        
        dm.callJNI_OnLoad(emulator);
        
        // 设置跟踪器
        setupTracing();
    }
    
    /**
     * 初始化地址标签
     */
    private void initializeAddressLabels() {
        addressLabels.put(module.base + SIGN_METHOD_ADDR, "SIGN_METHOD_ENTRY");
        addressLabels.put(module.base + FAIL_RETURN_ADDR, "FAIL_RETURN_POINT");
        addressLabels.put(module.base + 0x162f8L, "SIGN_METHOD+4");
        addressLabels.put(module.base + 0x16300L, "SIGN_METHOD+12");
        addressLabels.put(module.base + 0x16310L, "SIGN_METHOD+28");
        addressLabels.put(module.base + 0x16320L, "SIGN_METHOD+44");
        addressLabels.put(module.base + 0x16330L, "SIGN_METHOD+60");
        addressLabels.put(module.base + 0x16340L, "SIGN_METHOD+76");
        addressLabels.put(module.base + 0x16350L, "SIGN_METHOD+92");
        addressLabels.put(module.base + 0x16360L, "SIGN_METHOD+108");
        addressLabels.put(module.base + 0x16370L, "SIGN_METHOD+124");
        addressLabels.put(module.base + 0x16380L, "SIGN_METHOD+140");
        addressLabels.put(module.base + 0x16390L, "SIGN_METHOD+156");
        addressLabels.put(module.base + 0x163a0L, "SIGN_METHOD+172");
    }
    
    /**
     * 设置跟踪器
     */
    private void setupTracing() {
        System.out.println("[*] 设置指令和内存跟踪器...");
        
        Backend backend = emulator.getBackend();
        
        // 设置指令跟踪
        backend.hook_add_new(new com.github.unidbg.arm.backend.CodeHook() {
            @Override
            public void hook(Backend backend, long address, int size, Object user) {
                // 只跟踪我们关心的地址范围
                if (address >= module.base + SIGN_METHOD_ADDR && 
                    address <= module.base + FAIL_RETURN_ADDR) {
                    
                    String label = addressLabels.getOrDefault(address, "");
                    String instruction = ""; // 这里可以添加反汇编逻辑
                    String registers = getRegisterState(backend);
                    
                    InstructionTrace trace = new InstructionTrace(address, instruction, registers);
                    instructionTraces.add(trace);
                    
                    System.out.println("[指令] " + (label.isEmpty() ? "" : "[" + label + "] ") + 
                        String.format("0x%x", address));
                }
            }
        }, module.base + SIGN_METHOD_ADDR, module.base + FAIL_RETURN_ADDR, null);
        
        // 设置内存读取跟踪
        backend.hook_add_new(new com.github.unidbg.arm.backend.ReadHook() {
            @Override
            public void hook(Backend backend, long address, int size, long value, Object user) {
                MemoryAccess access = new MemoryAccess(address, "READ", value, size);
                memoryAccesses.add(access);
                
                // 只打印重要的内存访问
                if (isImportantMemoryAccess(address)) {
                    System.out.println("[内存读] " + access);
                }
            }
        }, 1, 0, null);
        
        // 设置内存写入跟踪
        backend.hook_add_new(new com.github.unidbg.arm.backend.WriteHook() {
            @Override
            public void hook(Backend backend, long address, int size, long value, Object user) {
                MemoryAccess access = new MemoryAccess(address, "WRITE", value, size);
                memoryAccesses.add(access);
                
                if (isImportantMemoryAccess(address)) {
                    System.out.println("[内存写] " + access);
                }
            }
        }, 1, 0, null);
        
        System.out.println("[*] 跟踪器设置完成");
    }
    
    /**
     * 获取寄存器状态
     */
    private String getRegisterState(Backend backend) {
        try {
            long x0 = backend.reg_read(com.github.unidbg.arm.Arm64Const.UC_ARM64_REG_X0);
            long x1 = backend.reg_read(com.github.unidbg.arm.Arm64Const.UC_ARM64_REG_X1);
            long x2 = backend.reg_read(com.github.unidbg.arm.Arm64Const.UC_ARM64_REG_X2);
            long lr = backend.reg_read(com.github.unidbg.arm.Arm64Const.UC_ARM64_REG_LR);
            
            return String.format("X0=0x%x X1=0x%x X2=0x%x LR=0x%x", x0, x1, x2, lr);
        } catch (Exception e) {
            return "寄存器读取失败: " + e.getMessage();
        }
    }
    
    /**
     * 判断是否是重要的内存访问
     */
    private boolean isImportantMemoryAccess(long address) {
        // 过滤掉一些不重要的内存访问
        // 只关注可能与验证相关的内存区域
        return address >= module.base && address <= module.base + module.size;
    }
    
    /**
     * 执行跟踪分析
     */
    public void performTraceAnalysis() {
        System.out.println("\n[*] 开始跟踪分析...");
        
        // 清空之前的跟踪数据
        instructionTraces.clear();
        memoryAccesses.clear();
        
        // 执行签名方法
        String result = callSignatureMethod("48de41784dad6ff20e42640f2d5b0151");
        
        System.out.println("\n[*] 签名结果: " + (result.isEmpty() ? "失败" : result));
        System.out.println("[*] 指令跟踪数量: " + instructionTraces.size());
        System.out.println("[*] 内存访问数量: " + memoryAccesses.size());
        
        // 分析跟踪数据
        analyzeTraceData();
        
        // 保存跟踪数据到文件
        saveTraceData();
    }
    
    /**
     * 分析跟踪数据
     */
    private void analyzeTraceData() {
        System.out.println("\n[*] ========== 跟踪数据分析 ==========");
        
        // 分析指令执行路径
        System.out.println("[分析] 指令执行路径:");
        for (int i = 0; i < Math.min(20, instructionTraces.size()); i++) {
            InstructionTrace trace = instructionTraces.get(i);
            String label = addressLabels.getOrDefault(trace.address, "");
            System.out.println("  " + (label.isEmpty() ? "" : "[" + label + "] ") + 
                String.format("0x%x", trace.address));
        }
        
        if (instructionTraces.size() > 20) {
            System.out.println("  ... (省略 " + (instructionTraces.size() - 20) + " 条指令)");
        }
        
        // 分析关键内存访问
        System.out.println("\n[分析] 关键内存访问:");
        memoryAccesses.stream()
            .filter(access -> isImportantMemoryAccess(access.address))
            .limit(10)
            .forEach(access -> System.out.println("  " + access));
        
        // 查找可能的验证失败点
        System.out.println("\n[分析] 查找验证失败模式...");
        findFailurePatterns();
    }
    
    /**
     * 查找失败模式
     */
    private void findFailurePatterns() {
        // 查找是否有特定的比较操作
        long compareCount = instructionTraces.stream()
            .filter(trace -> trace.address >= module.base + SIGN_METHOD_ADDR && 
                           trace.address < module.base + FAIL_RETURN_ADDR)
            .count();
        
        System.out.println("[模式] 验证区域内指令数量: " + compareCount);
        
        // 查找内存访问模式
        long memoryAccessInRange = memoryAccesses.stream()
            .filter(access -> access.address >= module.base + SIGN_METHOD_ADDR && 
                            access.address <= module.base + FAIL_RETURN_ADDR)
            .count();
        
        System.out.println("[模式] 验证区域内内存访问: " + memoryAccessInRange);
    }
    
    /**
     * 保存跟踪数据
     */
    private void saveTraceData() {
        try {
            // 保存指令跟踪
            FileWriter instructionWriter = new FileWriter("instruction_trace.log");
            instructionWriter.write("指令跟踪数据\n");
            instructionWriter.write("===================\n");
            for (InstructionTrace trace : instructionTraces) {
                String label = addressLabels.getOrDefault(trace.address, "");
                instructionWriter.write(String.format("0x%x: %s %s\n", 
                    trace.address, label, trace.registers));
            }
            instructionWriter.close();
            
            // 保存内存访问
            FileWriter memoryWriter = new FileWriter("memory_trace.log");
            memoryWriter.write("内存访问跟踪数据\n");
            memoryWriter.write("===================\n");
            for (MemoryAccess access : memoryAccesses) {
                memoryWriter.write(access.toString() + "\n");
            }
            memoryWriter.close();
            
            System.out.println("[*] 跟踪数据已保存到文件");
            
        } catch (IOException e) {
            System.out.println("[错误] 保存跟踪数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 调用签名方法
     */
    private String callSignatureMethod(String param) {
        try {
            DvmClass class_JNIFactory = vm.resolveClass("com/netease/NetSecKit/factory/JNIFactory");
            String method_w3270a03dafee4a0a = "w3270a03dafee4a0a(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/String;";

            DvmObject<?> obj = class_JNIFactory.callStaticJniMethodObject(
                    emulator,
                    method_w3270a03dafee4a0a,
                    vm.resolveClass("android/content/Context").newObject(null),
                    param
            );
            
            return obj.getValue().toString();
        } catch (Exception e) {
            System.out.println("[错误] 调用签名方法失败: " + e.getMessage());
            return "";
        }
    }
    
    // JNI环境补全方法
    @Override
    public int getStaticIntField(BaseVM vm, DvmClass dvmClass, String signature) {
        switch (signature) {
            case "com/netease/NetSecKit/poly/a->o:I":
                return 2;
            case "com/netease/NetSecKit/poly/a->p:I":
                return 12;
        }
        return super.getStaticIntField(vm, dvmClass, signature);
    }

    @Override
    public DvmObject<?> callObjectMethodV(BaseVM vm, DvmObject<?> dvmObject, String signature, VaList vaList) {
        switch (signature) {
            case "android/content/Context->getApplicationInfo()Landroid/content/pm/ApplicationInfo;":
                return vm.resolveClass("android/content/pm/ApplicationInfo").newObject(null);
        }
        return super.callObjectMethodV(vm, dvmObject, signature, vaList);
    }

    @Override
    public DvmObject<?> getObjectField(BaseVM vm, DvmObject<?> dvmObject, String signature) {
        switch (signature) {
            case "android/content/pm/ApplicationInfo->publicSourceDir:Ljava/lang/String;":
                return new StringObject(vm, "/data/app/com.netease.xyqcbg/base.apk");
        }
        return super.getObjectField(vm, dvmObject, signature);
    }
    
    public static void main(String[] args) {
        MemoryInstructionTracer tracer = new MemoryInstructionTracer();
        tracer.performTraceAnalysis();
    }
}
